import { useQuery } from "@tanstack/react-query";
import Main from "~/components/Main";
import { API_ORIGIN } from "~/constants";
import type { Settings } from "~/types/settings";

export default function Index() {
	const { data: settings } = useQuery<Settings>({
		queryKey: ["settings"],
		queryFn: () =>
			fetch(new URL("/settings", API_ORIGIN)).then((res) => res.json()),
		staleTime: Number.POSITIVE_INFINITY,
	});

	return (
		<>
			<Main settings={settings} />
		</>
	);
}
