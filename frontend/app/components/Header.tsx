import { useId } from "react";
import {
	TrafficLight,
	TrafficLightStatus,
} from "~/components/charts/TrafficLight";
import MaterialCreate from "~/components/dialogs/MaterialCreate";
import MaterialDelete from "~/components/dialogs/MaterialDelete";
import SettingsCreate from "~/components/dialogs/SettingsCreate";
import SettingsDelete from "~/components/dialogs/SettingsDelete";
import UpdateSettings from "~/components/dialogs/UpdateSettings";
import MaterialSelector from "~/components/selectors/MaterialSelector";
import SettingsSelector from "~/components/selectors/SettingsSelector";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import type { Settings } from "~/types/settings";

export interface HeaderProps {
	settings?: Settings;
	onSettingsChange: (settings: Settings) => void;
}

export function LeftHeader(settings?: Settings) {
	return (
		<div className="flex flex-nowrap gap-2 items-center">
			<span>Status: </span>
			<TrafficLight status={TrafficLightStatus.OK} />
		</div>
	);
}

export function CenterHeader({ settings, onSettingsChange }: HeaderProps) {
	return (
		<div className="flex flex-nowrap gap-2 items-center">
			<span>Settings:</span>
			<SettingsSelector value={settings} onChange={onSettingsChange} />
			<SettingsCreate />
			<SettingsDelete />
			{settings && (
				<UpdateSettings value={settings} onChange={onSettingsChange} />
			)}
		</div>
	);
}
export function RightHeader({ settings, onSettingsChange }: HeaderProps) {
	return (
		<div>
			<div className="flex items-center space-x-2">
				<Switch
					id={useId()}
					checked={settings?.admin}
					onCheckedChange={(checked) =>
						onSettingsChange({ ...settings, admin: checked })
					}
					className="cursor-pointer"
				/>
				<Label htmlFor="admin-mode">Admin Mode</Label>
			</div>
		</div>
	);
}

export default function Header({ settings, onSettingsChange }: HeaderProps) {
	return (
		<>
			<LeftHeader />
			<CenterHeader settings={settings} onSettingsChange={onSettingsChange} />
			<RightHeader settings={settings} onSettingsChange={onSettingsChange} />
		</>
	);
}
