import type { Settings } from "~/types/settings";

export interface ErrorLogProps {
	settings?: Settings;
}

export default function ErrorLog({ settings }: ErrorLogProps) {
	return (
		<ol>
			{Array.from({ length: 50 })
				.map((_, i) => {
					const types = [
						{ prefix: "INFO", color: "text-blue-600" },
						{ prefix: "WARNING", color: "text-yellow-600" },
						{ prefix: "ERROR", color: "text-red-600" },
					];
					const { prefix, color } = types[i % 3];
					return (
						<li key={i} className="mb-1">
							<span className={`${color} font-bold`}>{prefix}</span>
							{`: Beispiel-Logeintrag Nummer ${i + 1}`}
						</li>
					);
				})
				.reverse()}
		</ol>
	);
}
