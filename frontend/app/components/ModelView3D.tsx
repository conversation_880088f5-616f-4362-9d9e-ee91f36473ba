import { useQuery } from "@tanstack/react-query";
import { API_ORIGIN } from "~/constants";
import type { MachineData } from "~/types/machine_data";
import type { Settings } from "~/types/settings";

export interface ModelView3DProps {
	settings?: Settings;
}

export default function ModelView3D({ settings }: ModelView3DProps) {
	const { data } = useQuery<MachineData[]>({
		queryKey: ["get-machine-data", settings?.machine_name, settings?.part_uuid],
		queryFn: () => {
			const url = new URL("/machine-data", API_ORIGIN);
			if (settings?.machine_name) {
				url.searchParams.set("machine_name", settings.machine_name);
			}
			if (settings?.part_uuid) {
				url.searchParams.set("part_uuid", settings.part_uuid);
			}
			url.searchParams.set("every_nth", "250");
			url.searchParams.set("limit", "240");
			return fetch(url).then((res) => res.json());
		},
		refetchInterval: 1000,
	});

	console.log(data);

	return <></>;
}
