import {
	CornerLeftDown,
	CornerLeftUp,
	CornerRightDown,
	CornerRightUp,
} from "lucide-react";
import Gauge from "~/components/charts/Gauge";
import type { Settings } from "~/types/settings";

export interface MachineStatusProps {
	settings?: Settings;
}

export default function MachineStatus({ settings }: MachineStatusProps) {
	return (
		<>
			{settings?.admin ? (
				<div className="h-full flex justify-evenly items-center p-4">
					<div className="h-full w-1/5 flex-auto flex flex-col justify-center text-center">
						<h4 className="text-xl">Overall Condition</h4>
						<Gauge percent={0.86} />
					</div>
					<div className="h-full w-1/5 flex-auto flex flex-col justify-center text-center">
						<h4 className="text-xl">X-Axis Condition</h4>
						<Gauge percent={0.9} />
					</div>
					<div className="h-full w-1/5 flex-auto flex flex-col justify-center text-center">
						<h4 className="text-xl">Y-Axis Condition</h4>
						<Gauge percent={0.95} />
					</div>
					<div className="h-full w-1/5 flex-auto flex flex-col justify-center text-center">
						<h4 className="text-xl">Z-Axis Condition</h4>
						<Gauge percent={0.98} />
					</div>
					<div className="h-full w-1/5 flex-auto flex flex-col justify-center text-center">
						<h4 className="text-xl">SP-Axis Condition</h4>
						<Gauge percent={0.99} />
					</div>
				</div>
			) : (
				<div className="w-full h-full grid grid-cols-[30%_40%_30%] grid-rows-[30%_40%_30%] p-4">
					<div className="flex items-center justify-center text-center">
						<div className="h-72 w-72 flex flex-col justify-center text-center">
							<h4 className="text-xl">X-Axis Condition</h4>
							<Gauge percent={0.9} />
						</div>
					</div>
					<div className="flex justify-between items-end">
						<CornerRightDown size={100} />
						<CornerLeftDown size={100} />
					</div>
					<div className="flex items-center justify-center text-center">
						<div className="h-72 w-72 flex flex-col justify-center text-center">
							<h4 className="text-xl">Y-Axis Condition</h4>
							<Gauge percent={0.95} />
						</div>
					</div>
					<div />
					<div className="flex items-center justify-center text-center">
						<div className="h-full w-full flex flex-col justify-center text-center">
							<h4 className="text-3xl">Overall Condition</h4>
							<Gauge percent={0.86} />
						</div>
					</div>
					<div />
					<div className="flex items-center justify-center text-center">
						<div className="h-72 w-72 flex flex-col justify-center text-center">
							<h4 className="text-xl">Z-Axis Condition</h4>
							<Gauge percent={0.98} />
						</div>
					</div>
					<div className="flex justify-between items-start">
						<CornerRightUp size={100} />
						<CornerLeftUp size={100} />
					</div>
					<div className="flex items-center justify-center text-center">
						<div className="h-72 w-72 flex flex-col justify-center text-center">
							<h4 className="text-xl">SP-Axis Condition</h4>
							<Gauge percent={0.99} />
						</div>
					</div>
				</div>
			)}
		</>
	);
}
