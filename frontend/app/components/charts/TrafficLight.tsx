import { useMemo } from "react";
import { cn } from "~/lib/utils";
import type { Settings } from "~/types/settings";

export enum TrafficLightStatus {
	OK = "ok",
	WARNING = "warning",
	ERROR = "error",
}

export type TrafficLightProps = {
	status: TrafficLightStatus;
};

export function TrafficLight({ status }: TrafficLightProps) {
	const index = useMemo(() => {
		switch (status) {
			case TrafficLightStatus.OK:
				return 0;
			case TrafficLightStatus.WARNING:
				return 1;
			case TrafficLightStatus.ERROR:
				return 2;
			default:
				return -1; // Invalid status
		}
	}, [status]);
	return (
		<div className="flex flex-row items-center justify-start h-full w-48 bg-gray-800 gap-2 ">
			<div
				className={cn(
					"w-8 h-8 rounded-full transition-colors duration-300 bg-[#2dc937]",
					status === TrafficLightStatus.OK ? "opacity-100" : "opacity-40",
				)}
			/>
			<div
				className={cn(
					"w-8 h-8 rounded-full transition-colors duration-300 bg-[#e7b416]",
					status === TrafficLightStatus.WARNING ? "opacity-100" : "opacity-40",
				)}
			/>
			<div
				className={cn(
					"w-8 h-8 rounded-full transition-colors duration-300 bg-[#cc3232]",
					status === TrafficLightStatus.ERROR ? "opacity-100" : "opacity-40",
				)}
			/>
		</div>
	);
}
