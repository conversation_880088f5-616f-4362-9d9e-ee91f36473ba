import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton";
import { API_ORIGIN } from "~/constants";
import type { Material } from "~/types/material";

interface MaterialSelectorProps {
	onChange: (value: number) => void;
	value?: number;
}

export default function MaterialSelector({
	onChange,
	value,
}: MaterialSelectorProps) {
	const { data, isLoading } = useQuery<Material[]>({
		queryKey: ["get-materials"],
		queryFn: () =>
			fetch(new URL("/materials", API_ORIGIN)).then((res) => res.json()),
	});

	const sortedMaterials = useMemo(
		() =>
			data?.sort((a: Material, b: Material) => a.name.localeCompare(b.name)) ??
			[],
		[data],
	);

	const currentMaterial = useMemo(
		() => data?.find((a) => a.id === value),
		[data, value],
	);

	return (
		<div className="flex flex-col gap-1.5 w-full max-w-[200px]">
			{isLoading ? (
				<Skeleton className="h-10 w-32" />
			) : (
				<Select
					value={String(value)}
					onValueChange={(value: string) => onChange(Number.parseInt(value))}
				>
					<SelectTrigger className="w-full focus-visible:ring-0">
						<SelectValue placeholder={"Select Material..."}>
							<div className="flex items-center gap-2">
								{currentMaterial
									? currentMaterial.name
									: "No Material selected"}
							</div>
						</SelectValue>
					</SelectTrigger>
					<SelectContent>
						{sortedMaterials.map((material: Material) => (
							<SelectItem key={material.id} value={String(material.id)}>
								{material.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			)}
		</div>
	);
}
