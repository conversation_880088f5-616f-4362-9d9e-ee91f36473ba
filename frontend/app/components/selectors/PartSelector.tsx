import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton";
import { API_ORIGIN } from "~/constants";
import type { Part } from "~/types/part";

interface PartSelectorProps {
	onChange: (value: string) => void;
	value?: string;
}

export default function PartSelector({ onChange, value }: PartSelectorProps) {
	const { data, isLoading } = useQuery<Part[]>({
		queryKey: ["get-parts"],
		queryFn: () =>
			fetch(new URL("/parts", API_ORIGIN)).then((res) => res.json()),
	});

	const sortedParts = useMemo(
		() => data?.sort((a, b) => a.name.localeCompare(b.name)) ?? [],
		[data],
	);

	const currentPart = useMemo(
		() => data?.find((a) => a.uuid === value),
		[data, value],
	);

	return (
		<div className="flex flex-col gap-1.5 w-full max-w-[200px]">
			{isLoading ? (
				<Skeleton className="h-10 w-32" />
			) : (
				<Select
					value={value}
					onValueChange={(value: string) => onChange(value)}
				>
					<SelectTrigger className="w-full focus-visible:ring-0">
						<SelectValue placeholder={"Select Part..."}>
							<div className="flex items-center gap-2">
								{currentPart ? currentPart.name : "No Part selected"}
							</div>
						</SelectValue>
					</SelectTrigger>
					<SelectContent>
						{sortedParts.map((part) => (
							<SelectItem key={part.uuid} value={part.uuid}>
								{part.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			)}
		</div>
	);
}
