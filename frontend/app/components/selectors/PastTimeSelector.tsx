import { useQuery } from "@tanstack/react-query";
import dayjs, { type Dayjs } from "dayjs";
import { useMemo } from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton";
import { Slider } from "~/components/ui/slider";
import { API_ORIGIN } from "~/constants";
import type { Item } from "~/types/item";
import type { Material } from "~/types/material";

interface PastTimeSelectorProps {
	onChange: (value: Dayjs | null) => void;
	value?: Dayjs | null;
}

export default function PastTimeSelector({
	onChange,
	value,
}: PastTimeSelectorProps) {
	const { data, isLoading } = useQuery<Item[]>({
		queryKey: ["get-items"],
		queryFn: () =>
			fetch(new URL("/items", API_ORIGIN)).then((res) => res.json()),
	});
	console.log(data);

	const minTime = useMemo(() => {
		if (!data || data.length === 0) return null;
		return Math.min(...data.map((item) => item.start_time_unix));
	}, [data]);
	const maxTime = useMemo(() => {
		if (!data || data.length === 0) return null;
		return Math.max(
			...data.map((item) => item.end_time_unix || item.start_time_unix),
		);
	}, [data]);

	const valueFormatted = useMemo(() => {
		if (!value) return null;
		if (!value.isValid()) return null;
		const data = value.toDate();
		return `${data.toLocaleDateString()} ${data.toLocaleTimeString()}`;
	}, [value]);

	return (
		<div className="ms-auto text-center ps-4 h-16">
			<div className="flex h-full items-center justify-center gap-4">
				<span>Select Time</span>
				{isLoading ? (
					<Skeleton className="h-10 w-32" />
				) : (
					<>
						<Slider
							className="w-xl"
							defaultValue={[maxTime || 0]}
							min={minTime || 0}
							max={maxTime || 0}
							value={value ? [value.unix()] : [maxTime || 0]}
							onValueCommit={(v) => onChange(dayjs.unix(v[0]))}
							step={1}
						/>
						{valueFormatted ? (
							<span className="text-white">{valueFormatted}</span>
						) : (
							<>
								<span>Live</span>
								<span className="animate-ping h-2 w-2 bg-red-500 rounded-full"></span>
							</>
						)}
					</>
				)}
			</div>
		</div>
	);
}
