import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton";
import { API_ORIGIN } from "~/constants";
import type { Material } from "~/types/material";
import type { Settings } from "~/types/settings";

interface SettingsSelectorProps {
	onChange: (value: Settings) => void;
	value?: Settings;
}

export default function SettingsSelector({
	onChange,
	value,
}: SettingsSelectorProps) {
	const { data, isLoading } = useQuery<Settings[]>({
		queryKey: ["get-settings"],
		queryFn: () =>
			fetch(new URL("/settings", API_ORIGIN)).then((res) => res.json()),
	});

	const sortedSettings = useMemo(
		() =>
			data?.sort((a: Settings, b: Settings) =>
				(a.name ?? "").localeCompare(b.name ?? ""),
			) ?? [],
		[data],
	);

	const currentSettings = useMemo(
		() => data?.find((a) => a.id === value?.id),
		[data, value],
	);

	return (
		<div className="flex flex-col gap-1.5 w-full max-w-[200px]">
			{isLoading ? (
				<Skeleton className="h-10 w-32" />
			) : (
				<Select
					value={String(value?.id)}
					onValueChange={(value: string) => {
						const settings =
							data?.find((a) => a.id === Number.parseInt(value)) ?? data?.[0];
						settings && onChange(settings);
					}}
				>
					<SelectTrigger className="w-full focus-visible:ring-0">
						<SelectValue placeholder={"Select Material..."}>
							<div className="flex items-center gap-2">
								{currentSettings
									? currentSettings.name
									: "No Settings selected"}
							</div>
						</SelectValue>
					</SelectTrigger>
					<SelectContent>
						{sortedSettings.map((settings: Settings) => (
							<SelectItem key={settings.id} value={String(settings.id)}>
								{settings.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			)}
		</div>
	);
}
