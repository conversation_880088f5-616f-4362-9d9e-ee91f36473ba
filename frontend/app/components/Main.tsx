import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import type { Dayjs } from "dayjs";
import { useState } from "react";
import { Toaster, toast } from "sonner";
import ErrorLog from "~/components/ErrorLog";
import Header from "~/components/Header";
import MachineStatus from "~/components/MachineStatus";
import { API_ORIGIN } from "~/constants";
import { cn } from "~/lib/utils";
import type { Settings } from "~/types/settings";
import ModelView3D from "./ModelView3D";
import PastTimeSelector from "./selectors/PastTimeSelector";

export interface MainProps {
	settings?: Settings;
}

export default function Main({ settings: settings_ }: MainProps) {
	const [settings, setSettings] = useState(settings_);
	const [pastTime, setPastTime] = useState<Dayjs | null>();

	const { data } = useQuery<Settings[]>({
		queryKey: ["get-settings"],
		queryFn: () =>
			fetch(new URL("/settings", API_ORIGIN)).then((res) => res.json()),
	});

	async function handleSettingsUpdate(newSettings: Settings) {
		console.debug("Updating settings...");
		const status = await axios
			.post(`${API_ORIGIN}/settings`, newSettings)
			.then((res) => res.status);
		if (status === 200) {
			console.info("Settings updated");
			toast.success("Settings updated!");
			setSettings(newSettings);
		} else {
			console.error("Failed to update settings");
			toast.error("Failed to update settings!");
		}
	}

	if (!settings && data?.[0]) {
		setSettings(data?.[0]);
	}

	return (
		<>
			<header className="flex justify-between items-center w-full h-16 bg-gray-800 text-white px-2">
				<Header settings={settings} onSettingsChange={handleSettingsUpdate} />
			</header>

			<main
				className={cn(
					"grid grid-cols-[40%_60%] grid-rows-[30%_70%] gap-2 p-2 items-center",
					settings?.admin ? "h-[calc(100vh-9rem)]" : "h-[calc(100vh-5rem)]",
				)}
			>
				<div className="h-full w-full border-2 border-primary">
					<h3 className="w-full bg-secondary p-2 text-lg h-10">Error Log</h3>
					<div className="h-[calc(100%-2.5rem)] overflow-y-auto ps-2">
						<ErrorLog settings={settings} />
					</div>
				</div>
				<div
					className={cn(
						"h-full w-full border-2 border-primary",
						!settings?.admin && "row-span-2",
					)}
				>
					<h3 className="w-full bg-secondary p-2 text-lg">Machine Status</h3>
					<div className="h-[calc(100%-2.5rem)] overflow-y-auto ps-2">
						<MachineStatus settings={settings} />
					</div>
				</div>
				<div className="h-full w-full border-2 border-primary">
					<h3 className="w-full bg-secondary p-2 text-lg">Model View</h3>
					<div className="h-[calc(100%-2.5rem)] overflow-y-auto ps-2">
						<ModelView3D settings={settings} />
					</div>
				</div>
				{settings?.admin && (
					<div className="h-full w-full border-2 border-primary">
						<h3 className="w-full bg-secondary p-2 text-lg">Admin Panel</h3>
						<div className="h-[calc(100%-2.5rem)] overflow-y-auto ps-2">
							<p>Admin functionalities will be implemented here.</p>
						</div>
					</div>
				)}
			</main>

			{settings?.admin && (
				<footer className="h-16">
					<PastTimeSelector value={pastTime} onChange={setPastTime} />
				</footer>
			)}
			<Toaster />
		</>
	);
}
