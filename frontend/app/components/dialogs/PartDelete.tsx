import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import PartSelector from "~/components/selectors/PartSelector";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { API_ORIGIN } from "~/constants";
import { Button } from "../ui/button";

export interface PartDeleteProps {
	onDeleted?: (value: string) => void;
}

export default function PartDelete({ onDeleted }: PartDeleteProps) {
	const queryClient = useQueryClient();
	const [part, setPart] = useState<string | undefined>();

	async function submit() {
		console.debug("Deleting part...");
		if (!part) {
			toast.error("No Part selected");
			return;
		}
		const status = await axios
			.delete(`${API_ORIGIN}/parts/${part}`)
			.then((res) => res.status);
		if (status === 200) {
			console.info("Part deleted");
			toast.success("Part deleted!");
			await queryClient.refetchQueries({ queryKey: ["get-parts"] });
			onDeleted?.(part);
		} else {
			console.error("Failed to delete Part");
			toast.error("Failed to delete Part!");
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="destructive" className="rounded">
					<MinusCircleIcon />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Delete Part</DialogTitle>
					<DialogDescription>Delete a Part by selecting it.</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="grid gap-3">
						<PartSelector onChange={setPart} value={part} />
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							type="submit"
							variant="destructive"
							className="cursor-pointer"
							onClick={submit}
						>
							Delete Part
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
