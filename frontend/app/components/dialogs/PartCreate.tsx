import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { PlusCircleIcon } from "lucide-react";
import { useId, useState } from "react";
import { toast } from "sonner";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { API_ORIGIN } from "~/constants";
import type { Part } from "~/types/part";
import { Button } from "../ui/button";

export interface PartCreateProps {
	onCreated: (value: string) => void;
}

export default function PartCreate({ onCreated }: PartCreateProps) {
	const queryClient = useQueryClient();
	const [name, setName] = useState<string | undefined>();
	const [stlFile, setStlFile] = useState<File | undefined>();
	const [gCodeFile, setGCodeFile] = useState<File | undefined>();

	async function submit() {
		console.debug("Creating new part...");
		if (name && name.length < 3) {
			toast.error("Part Name must be at least 3 characters long!");
			return;
		}
		if (!stlFile) {
			toast.error("No STL-File selected!");
			return;
		}
		if (!gCodeFile) {
			toast.error("No G-Code File selected!");
			return;
		}
		try {
			const formData = new FormData();
			formData.append("part", JSON.stringify({ name }));
			formData.append("stl_file", stlFile);
			formData.append("gcode_file", gCodeFile);
			const newPart: Part = await axios
				.post(`${API_ORIGIN}/parts`, formData, {
					headers: {
						"Content-Type": "multipart/form-data",
					},
				})
				.then((r) => r.data);
			console.info("New Part", newPart);
			toast.success(`Part ${newPart.name} successfully created!`);
			await queryClient.refetchQueries({ queryKey: ["get-parts"] });
			onCreated(newPart.uuid);
		} catch (error) {
			console.error(`Failed to create Part: ${name}`, error);
			toast.error(`Failed to create Part: ${name}`);
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="affirmative" className="rounded">
					<PlusCircleIcon />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Create Part</DialogTitle>
					<DialogDescription>
						Create a new Part by specifying it's name.
					</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="grid gap-3">
						<Label htmlFor="name">Name</Label>
						<Input
							id={useId()}
							name="name"
							value={name}
							onChange={(n) => setName(n.target.value)}
						/>
					</div>
					<div className="grid gap-3">
						<Label htmlFor="stl">STL-File</Label>
						<Input
							id={useId()}
							name="stl"
							type="file"
							accept=".stl"
							onChange={(n) => n.target.files && setStlFile(n.target.files[0])}
						/>
					</div>
					<div className="grid gap-3">
						<Label htmlFor="stl">G-Code</Label>
						<Input
							id={useId()}
							name="gcode"
							type="file"
							onChange={(n) =>
								n.target.files && setGCodeFile(n.target.files[0])
							}
						/>
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							variant="affirmative"
							type="submit"
							className="cursor-pointer"
							onClick={submit}
						>
							Save changes
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
