import { useState } from "react";
import MaterialCreate from "~/components/dialogs/MaterialCreate";
import MaterialDelete from "~/components/dialogs/MaterialDelete";
import PartCreate from "~/components/dialogs/PartCreate";
import PartDelete from "~/components/dialogs/PartDelete";
import MaterialSelector from "~/components/selectors/MaterialSelector";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import type { Settings } from "~/types/settings";
import PartSelector from "../selectors/PartSelector";
import { Button } from "../ui/button";

export interface UpdateSettingsProps {
	value: Settings;
	onChange: (value: Settings) => void;
}

export default function UpdateSettings({
	value,
	onChange,
}: UpdateSettingsProps) {
	const [settings, setSettings] = useState(value);

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="default" className="cursor-pointer rounded">
					Update
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Settings</DialogTitle>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Part:</span>
						<PartSelector
							onChange={(part_uuid) => setSettings({ ...settings, part_uuid })}
							value={settings?.part_uuid}
						/>
						<PartCreate
							onCreated={(part_uuid) => setSettings({ ...settings, part_uuid })}
						/>
						<PartDelete
							onDeleted={(part_uuid) => {
								settings?.part_uuid === part_uuid &&
									setSettings({ ...settings, part_uuid: undefined });
							}}
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Material:</span>
						<MaterialSelector
							onChange={(material_id) =>
								setSettings({ ...settings, material_id })
							}
							value={settings?.material_id}
						/>
						<MaterialCreate
							onCreated={(material_id) =>
								setSettings({ ...settings, material_id })
							}
						/>
						<MaterialDelete
							onDeleted={(material_id) => {
								settings?.material_id === material_id &&
									setSettings({ ...settings, material_id: undefined });
							}}
						/>
					</div>
					{/*	machine_name: string;
	tool_radius: number;
	tool_num_teeth: number;
	specific_cutting_force: number;
	specific_feeding_force: number;
	specific_passive_force: number;
	specific_wear_correction: number;
	specific_cooling_correction: number;
	force_correction_x: number;
	force_correction_y: number;
	force_correction_z: number;
	machine_coef_x: number;
	machine_coef_y: number;
	machine_coef_z: number;*/}
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Machine Name:</span>
						<input
							type="text"
							value={settings?.machine_name ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									machine_name: e.target.value,
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Tool Radius (mm):</span>
						<input
							type="number"
							value={settings?.tool_radius ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									tool_radius: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Tool Teeth:</span>
						<input
							type="number"
							value={settings?.tool_num_teeth ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									tool_num_teeth: parseInt(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>
							Specific Cutting Force (N/mm<sup>2</sup>):
						</span>
						<input
							type="number"
							value={settings?.specific_cutting_force ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									specific_cutting_force: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>
							Specific Feeding Force (N/mm<sup>2</sup>):
						</span>
						<input
							type="number"
							value={settings?.specific_feeding_force ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									specific_feeding_force: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>
							Specific Passive Force (N/mm<sup>2</sup>):
						</span>
						<input
							type="number"
							value={settings?.specific_passive_force ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									specific_passive_force: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Specific Wear Correction:</span>
						<input
							type="number"
							value={settings?.specific_wear_correction ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									specific_wear_correction: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Specific Cooling Correction:</span>
						<input
							type="number"
							value={settings?.specific_cooling_correction ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									specific_cooling_correction: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Force Correction X:</span>
						<input
							type="number"
							value={settings?.force_correction_x ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									force_correction_x: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Force Correction Y:</span>
						<input
							type="number"
							value={settings?.force_correction_y ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									force_correction_y: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Force Correction Z:</span>
						<input
							type="number"
							value={settings?.force_correction_z ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									force_correction_z: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Machine Coefficient X:</span>
						<input
							type="number"
							value={settings?.machine_coef_x ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									machine_coef_x: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Machine Coefficient Y:</span>
						<input
							type="number"
							value={settings?.machine_coef_y ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									machine_coef_y: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
					<div className="flex flex-nowrap gap-2 items-center">
						<span>Machine Coefficient Z:</span>
						<input
							type="number"
							value={settings?.machine_coef_z ?? ""}
							onChange={(e) =>
								setSettings({
									...settings,
									machine_coef_z: parseFloat(e.target.value),
								})
							}
							className="border rounded px-2 py-1"
						/>
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							variant="affirmative"
							type="submit"
							className="cursor-pointer"
							onClick={() => {
								console.log(settings);
								onChange(settings);
							}}
						>
							Save changes
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
