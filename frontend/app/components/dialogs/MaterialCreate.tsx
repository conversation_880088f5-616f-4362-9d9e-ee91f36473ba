import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { PlusCircleIcon } from "lucide-react";
import { useId, useState } from "react";
import { toast } from "sonner";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { API_ORIGIN } from "~/constants";
import type { Material } from "~/types/material";
import { Button } from "../ui/button";

export interface MaterialCreateProps {
	onCreated: (value: number) => void;
}

export default function MaterialCreate({ onCreated }: MaterialCreateProps) {
	const queryClient = useQueryClient();
	const [name, setName] = useState<string>("");

	async function submit() {
		console.debug("Creating new material...");
		if (name.length < 3) {
			toast.error("Material Name must be at least 3 characters long!");
			return;
		}
		try {
			const newMaterial: Material = await axios
				.post(`${API_ORIGIN}/materials`, { name })
				.then((r) => r.data);
			console.info("New Material", newMaterial);
			toast.success(`Material ${newMaterial.name} successfully created!`);
			await queryClient.refetchQueries({ queryKey: ["get-materials"] });
			onCreated(newMaterial.id);
		} catch (error) {
			console.error(`Failed to create Material: ${name}`, error);
			toast.error(`Failed to create Material: ${name}`);
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="affirmative" className="rounded">
					<PlusCircleIcon />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Create Material</DialogTitle>
					<DialogDescription>
						Create a new Material by specifying it's name.
					</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="grid gap-3">
						<Label htmlFor="name">Name</Label>
						<Input
							id={useId()}
							name="name"
							value={name}
							onChange={(n) => setName(n.target.value)}
						/>
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							variant="affirmative"
							type="submit"
							className="cursor-pointer"
							onClick={submit}
						>
							Save changes
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
