import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import MaterialSelector from "~/components/selectors/MaterialSelector";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { API_ORIGIN } from "~/constants";
import { Button } from "../ui/button";

export interface MaterialDeleteProps {
	onDeleted?: (value: number) => void;
}

export default function MaterialDelete({ onDeleted }: MaterialDeleteProps) {
	const queryClient = useQueryClient();
	const [material, setMaterial] = useState<number | undefined>();

	async function submit() {
		console.debug("Deleting material...");
		if (!material) {
			toast.error("No Material selected");
			return;
		}
		const status = await axios
			.delete(`${API_ORIGIN}/materials/${material}`)
			.then((res) => res.status);
		if (status === 200) {
			console.info("Material deleted");
			toast.success("Material deleted!");
			await queryClient.refetchQueries({ queryKey: ["get-materials"] });
			onDeleted?.(material);
		} else {
			console.error("Failed to delete Material");
			toast.error("Failed to delete Material!");
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="destructive" className="rounded">
					<MinusCircleIcon />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Delete Material</DialogTitle>
					<DialogDescription>
						Delete a Material by selecting it.
					</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="grid gap-3">
						<MaterialSelector onChange={setMaterial} value={material} />
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							type="submit"
							variant="destructive"
							className="cursor-pointer"
							onClick={submit}
						>
							Delete Material
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
