import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import SettingsSelector from "~/components/selectors/SettingsSelector";
import {
	<PERSON>alog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { API_ORIGIN } from "~/constants";
import type { Settings } from "~/types/settings";
import { Button } from "../ui/button";

export interface SettingsDeleteProps {
	onDeleted?: (value: Settings) => void;
}

export default function SettingsDelete({ onDeleted }: SettingsDeleteProps) {
	const queryClient = useQueryClient();
	const [settings, setSettings] = useState<Settings | undefined>();

	async function submit() {
		console.debug("Creating new settings...");
		if (!settings) {
			toast.error("No Settings selected");
			return;
		}
		const status = await axios
			.delete(`${API_ORIGIN}/settings/${settings.id}`)
			.then((res) => res.status);
		if (status === 200) {
			console.info("Settings deleted");
			toast.success("Settings deleted!");
			await queryClient.refetchQueries({ queryKey: ["get-Settingss"] });
			onDeleted?.(settings);
		} else {
			console.error("Failed to delete Settings");
			toast.error("Failed to delete Settings!");
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="destructive" className="rounded">
					<MinusCircleIcon />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Delete Settings</DialogTitle>
					<DialogDescription>
						Delete a Settings by selecting it.
					</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4">
					<div className="grid gap-3">
						<SettingsSelector onChange={setSettings} value={settings} />
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="outline" className="cursor-pointer">
							Cancel
						</Button>
					</DialogClose>
					<DialogClose asChild>
						<Button
							type="submit"
							variant="destructive"
							className="cursor-pointer"
							onClick={submit}
						>
							Delete Settings
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
