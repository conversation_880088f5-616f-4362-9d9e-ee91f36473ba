{"name": "frontend", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@react-router/fs-routes": "^7.6.3", "@react-router/node": "^7.6.3", "@react-router/serve": "^7.6.3", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "isbot": "^5.1.28", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-gauge-chart": "^0.5.1", "react-router": "^7.6.3", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@react-router/dev": "^7.6.3", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query-devtools": "^5.83.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=20.0.0"}}